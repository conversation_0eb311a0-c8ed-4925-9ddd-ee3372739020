# HMM Activity Recognition - Presentation Outline

**6-8 Minute Demo for CS280 Project**

## Slide 1: Title & Team
- **Title**: Hidden Markov Models for Human Activity Recognition
- **Course**: CS280 - Fall 2025
- **Program**: SMU MedTech
- **Dataset**: UCI Human Activity Recognition

## Slide 2: Problem & Motivation
- **Problem**: Recognize human activities from smartphone sensor data
- **Applications**: 
  - Health monitoring and fitness tracking
  - Elderly care and fall detection
  - Smart home automation
  - Medical rehabilitation
- **Challenge**: Noisy sensor data, temporal dependencies, hidden states

## Slide 3: Dataset & Preprocessing
- **UCI HAR Dataset**: 30 subjects, 6 activities, 561 features
- **Original Activities**: WALKING, SITTING, STANDING, LAYING, WALKING_UPSTAIRS, WALKING_DOWNSTAIRS
- **Simplified States**: 3 states (Walking, Stationary, Laying)
- **Data**: 7,352 training + 2,947 test samples
- **Features**: Reduced from 561 to 15-20 key features
- **Preprocessing**: Standardization, feature selection

## Slide 4: HMM Model Specification
- **Hidden States**: S = {Walking, Stationary, Laying}
- **Observations**: 15-dimensional feature vectors from accelerometer/gyroscope
- **Emission Model**: Multivariate Gaussian distributions
- **Parameters**:
  - Initial probabilities π (3×1)
  - Transition matrix A (3×3)
  - Emission parameters μ, Σ (means and covariances)
- **Assumptions**: Markov property, Gaussian emissions, stationary process

## Slide 5: Training & Model Selection
- **Training Algorithm**: Baum-Welch (Expectation-Maximization)
- **Model Comparison**: Tested 2, 3, 4, and 5 states
- **Selection Criteria**: BIC (Bayesian Information Criterion)
- **Results**: 3-4 states optimal balance of complexity vs. performance
- **Convergence**: All models converged successfully

## Slide 6: Inference & Forecasting
- **Inference Methods**:
  - Forward-Backward: State probabilities
  - Viterbi: Most likely state sequence
- **Forecasting**: Multi-step ahead prediction
- **Evaluation Metrics**: Accuracy, log-likelihood, AIC/BIC
- **Performance**: 50-70% accuracy on test data

## Slide 7: Results & Visualizations
- **Model Performance**:
  - Test accuracy: ~60% (varies by configuration)
  - Successful convergence and learning
  - Clear state differentiation
- **Key Visualizations**:
  - Transition matrix heatmap
  - State sequence predictions
  - Emission parameter distributions
  - Forecasting results

## Slide 8: Conclusions & Future Work
- **Key Findings**:
  - HMM successfully learns activity patterns from real sensor data
  - Model distinguishes between walking, stationary, and laying activities
  - Forecasting capability enables prediction of future activities
  - Real-world dataset demonstrates practical applicability

- **Limitations**:
  - Simplified to 3 states (vs. 6 original activities)
  - Gaussian assumption may not capture all data complexity
  - Performance varies with feature selection and preprocessing

- **Future Work**:
  - Include more complex activities
  - Compare with deep learning approaches (LSTM/RNN)
  - Real-time implementation for mobile devices
  - Online learning for adaptive models

---

## Demo Script (6-8 minutes)

### Opening (1 minute)
"Today I'll demonstrate a Hidden Markov Model for human activity recognition using real smartphone sensor data from the UCI dataset."

### Problem & Data (1.5 minutes)
"The challenge is to recognize activities like walking, sitting, and laying from noisy accelerometer and gyroscope readings. We used the UCI HAR dataset with over 10,000 samples from 30 subjects."

### Model Explanation (2 minutes)
"Our HMM has 3 hidden states representing activity types, with Gaussian emission distributions for the 15-dimensional feature vectors. We use the Baum-Welch algorithm for training and Viterbi for decoding."

### Results Demo (2 minutes)
"Let me show you the results..." [Run demo_hmm_project.py]
- Model comparison and selection
- Training convergence
- State predictions and accuracy
- Forecasting demonstration

### Conclusions (1.5 minutes)
"The HMM successfully learned activity patterns with 60% accuracy, demonstrating the effectiveness of probabilistic models for temporal sequence analysis in real-world applications."

### Q&A (Remaining time)

---

## Key Points to Emphasize
1. **Real Dataset**: Using actual smartphone sensor data, not synthetic
2. **Complete Pipeline**: From raw data to trained model to predictions
3. **Model Selection**: Systematic comparison using information criteria
4. **Practical Application**: Relevant to health monitoring and smart devices
5. **Technical Rigor**: Proper HMM implementation with all key algorithms
