"""
Data Loader for Human Activity Recognition HMM Project
Loads and preprocesses UCI HAR dataset from Kaggle
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Tuple, List
import os

class UCIHARDataLoader:
    """Load and preprocess UCI Human Activity Recognition dataset"""

    def __init__(self, data_dir='archive'):
        self.data_dir = data_dir
        self.train_path = os.path.join(data_dir, 'train.csv')
        self.test_path = os.path.join(data_dir, 'test.csv')

        # For HMM, we'll simplify to 3 main activity states
        self.activity_mapping = {
            'WALKING': 0,
            'WALKING_UPSTAIRS': 0,  # Group as walking
            'WALKING_DOWNSTAIRS': 0,  # Group as walking
            'SITTING': 1,
            'STANDING': 1,  # Group as stationary
            'LAYING': 2
        }

        self.activity_names = ['Walking', 'Stationary', 'Laying']

    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Load train and test datasets"""

        if not os.path.exists(self.train_path) or not os.path.exists(self.test_path):
            raise FileNotFoundError(f"Dataset files not found in {self.data_dir}. Please ensure train.csv and test.csv are present.")

        print("Loading UCI HAR dataset...")
        train_df = pd.read_csv(self.train_path)
        test_df = pd.read_csv(self.test_path)

        print(f"Train set: {train_df.shape[0]} samples, {train_df.shape[1]} features")
        print(f"Test set: {test_df.shape[0]} samples, {test_df.shape[1]} features")

        return train_df, test_df

    def preprocess_data(self, df: pd.DataFrame, fit_scaler: bool = False) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Preprocess the dataset for HMM training

        Args:
            df: Input dataframe
            fit_scaler: Whether to fit the scaler (True for training data)

        Returns:
            observations: Processed feature matrix
            activities: Original activity labels
            states: Simplified state labels (0, 1, 2)
        """

        # Separate features and labels
        feature_cols = [col for col in df.columns if col not in ['subject', 'Activity']]
        X = df[feature_cols].values
        activities = df['Activity'].values

        # Map activities to simplified states
        states = np.array([self.activity_mapping[activity] for activity in activities])

        # Simple standardization (mean=0, std=1)
        if fit_scaler:
            self.feature_means = np.mean(X, axis=0)
            self.feature_stds = np.std(X, axis=0)
            print("Computed feature statistics for scaling")

        # Apply standardization
        X_scaled = (X - self.feature_means) / (self.feature_stds + 1e-8)  # Add small epsilon to avoid division by zero

        return X_scaled, activities, states

    def select_key_features(self, X: np.ndarray, n_features: int = 20) -> np.ndarray:
        """
        Select key features for HMM (reduce dimensionality)
        Focus on accelerometer and gyroscope mean/std features
        """

        # For simplicity, we'll use the first n_features which include
        # body acceleration means and stds (most relevant for activity recognition)
        return X[:, :n_features]

    def create_sequences(self, X: np.ndarray, states: np.ndarray,
                        sequence_length: int = 50, overlap: int = 25) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """
        Create overlapping sequences for HMM training

        Args:
            X: Feature matrix
            states: State labels
            sequence_length: Length of each sequence
            overlap: Overlap between sequences

        Returns:
            sequences: List of feature sequences
            sequence_states: List of corresponding state sequences
        """

        sequences = []
        sequence_states = []

        step = sequence_length - overlap

        for i in range(0, len(X) - sequence_length + 1, step):
            seq_X = X[i:i + sequence_length]
            seq_states = states[i:i + sequence_length]

            sequences.append(seq_X)
            sequence_states.append(seq_states)

        print(f"Created {len(sequences)} sequences of length {sequence_length}")
        return sequences, sequence_states

    def plot_data(self, X: np.ndarray, states: np.ndarray, activities: np.ndarray,
                  n_samples_plot: int = 500):
        """Plot the UCI HAR dataset"""

        fig, axes = plt.subplots(4, 1, figsize=(15, 10))

        time_steps = range(min(n_samples_plot, len(X)))

        # Plot first few features (body acceleration means)
        feature_names = ['tBodyAcc-mean()-X', 'tBodyAcc-mean()-Y', 'tBodyAcc-mean()-Z']
        for i in range(3):
            axes[i].plot(time_steps, X[:n_samples_plot, i], alpha=0.7)
            axes[i].set_ylabel(feature_names[i])
            axes[i].grid(True)

        # Plot activity states
        axes[3].plot(time_steps, states[:n_samples_plot], 'o-', markersize=2, alpha=0.7)
        axes[3].set_ylabel('Activity State')
        axes[3].set_xlabel('Sample Index')
        axes[3].set_yticks([0, 1, 2])
        axes[3].set_yticklabels(self.activity_names)
        axes[3].grid(True)

        # Add activity labels as background colors
        unique_activities = np.unique(activities[:n_samples_plot])
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_activities)))

        for i, activity in enumerate(unique_activities):
            mask = activities[:n_samples_plot] == activity
            if np.any(mask):
                indices = np.where(mask)[0]
                for ax in axes:
                    ax.axvspan(indices[0], indices[-1], alpha=0.1, color=colors[i], label=activity)

        axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.tight_layout()
        plt.savefig('uci_har_visualization.png', dpi=150, bbox_inches='tight')
        plt.show()

def main():
    """Load and preprocess UCI HAR dataset"""

    # Create data loader
    loader = UCIHARDataLoader(data_dir='archive')

    # Load datasets
    train_df, test_df = loader.load_data()

    # Preprocess training data
    print("\nPreprocessing training data...")
    train_X, train_activities, train_states = loader.preprocess_data(train_df, fit_scaler=True)

    # Preprocess test data
    print("Preprocessing test data...")
    test_X, test_activities, test_states = loader.preprocess_data(test_df, fit_scaler=False)

    # Select key features for HMM (reduce dimensionality)
    print("\nSelecting key features...")
    train_X_reduced = loader.select_key_features(train_X, n_features=20)
    test_X_reduced = loader.select_key_features(test_X, n_features=20)

    # Print data statistics
    print("\nDataset Statistics:")
    print(f"Training samples: {len(train_X_reduced)}")
    print(f"Test samples: {len(test_X_reduced)}")
    print(f"Features: {train_X_reduced.shape[1]}")
    print(f"Original activities: {np.unique(train_activities)}")
    print(f"Simplified states: {loader.activity_names}")

    print("\nActivity distribution (training):")
    unique_activities, counts = np.unique(train_activities, return_counts=True)
    for activity, count in zip(unique_activities, counts):
        print(f"  {activity}: {count}")

    print("\nState distribution (training):")
    unique_states, counts = np.unique(train_states, return_counts=True)
    for state, count in zip(unique_states, counts):
        print(f"  {loader.activity_names[state]}: {count}")

    # Plot sample of data
    print("\nCreating data visualization...")
    loader.plot_data(train_X_reduced, train_states, train_activities, n_samples_plot=500)

    # Save processed data
    print("\nSaving processed data...")
    os.makedirs('data', exist_ok=True)

    np.save('data/train_X.npy', train_X_reduced)
    np.save('data/train_states.npy', train_states)
    np.save('data/test_X.npy', test_X_reduced)
    np.save('data/test_states.npy', test_states)

    print("Processed data saved to data/ directory")

    return train_X_reduced, train_states, test_X_reduced, test_states

if __name__ == "__main__":
    train_X, train_states, test_X, test_states = main()
