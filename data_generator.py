"""
Data Generator for Human Activity Recognition HMM Project
Generates synthetic accelerometer data for three activities: walking, sitting, running
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Tuple, List
import os

class ActivityDataGenerator:
    """Generate synthetic accelerometer data for human activities"""
    
    def __init__(self, random_seed=42):
        np.random.seed(random_seed)
        
        # Define activity parameters (mean and std for accelerometer readings)
        self.activity_params = {
            'sitting': {'mean': [0.2, 0.1, 9.8], 'std': [0.3, 0.3, 0.5]},
            'walking': {'mean': [1.5, 0.8, 9.5], 'std': [1.2, 1.0, 1.5]},
            'running': {'mean': [3.0, 2.5, 8.5], 'std': [2.0, 2.2, 2.8]}
        }
        
        # Transition probabilities between activities
        self.transition_matrix = np.array([
            [0.7, 0.2, 0.1],  # sitting -> [sitting, walking, running]
            [0.3, 0.6, 0.1],  # walking -> [sitting, walking, running]
            [0.1, 0.3, 0.6]   # running -> [sitting, walking, running]
        ])
        
        self.activities = ['sitting', 'walking', 'running']
        
    def generate_sequence(self, n_samples: int = 1000) -> Tuple[np.ndarray, List[str], List[int]]:
        """
        Generate a sequence of accelerometer readings with hidden activity states
        
        Args:
            n_samples: Number of time steps to generate
            
        Returns:
            observations: (n_samples, 3) array of [accel_x, accel_y, accel_z]
            activity_sequence: List of activity names
            state_sequence: List of state indices (0=sitting, 1=walking, 2=running)
        """
        
        observations = np.zeros((n_samples, 3))
        activity_sequence = []
        state_sequence = []
        
        # Start with random activity
        current_state = np.random.choice(3)
        
        for t in range(n_samples):
            # Record current state
            current_activity = self.activities[current_state]
            activity_sequence.append(current_activity)
            state_sequence.append(current_state)
            
            # Generate observation based on current activity
            params = self.activity_params[current_activity]
            observation = np.random.normal(params['mean'], params['std'])
            observations[t] = observation
            
            # Transition to next state
            if t < n_samples - 1:
                current_state = np.random.choice(3, p=self.transition_matrix[current_state])
        
        return observations, activity_sequence, state_sequence
    
    def save_data(self, observations: np.ndarray, activity_sequence: List[str], 
                  state_sequence: List[int], filename: str = 'activity_data.csv'):
        """Save generated data to CSV file"""
        
        df = pd.DataFrame({
            'timestamp': range(len(observations)),
            'accel_x': observations[:, 0],
            'accel_y': observations[:, 1], 
            'accel_z': observations[:, 2],
            'activity': activity_sequence,
            'state_id': state_sequence
        })
        
        df.to_csv(filename, index=False)
        print(f"Data saved to {filename}")
        return df
    
    def plot_data(self, observations: np.ndarray, activity_sequence: List[str], 
                  n_samples_plot: int = 200):
        """Plot the generated accelerometer data"""
        
        fig, axes = plt.subplots(4, 1, figsize=(12, 10))
        
        time_steps = range(min(n_samples_plot, len(observations)))
        
        # Plot accelerometer components
        for i, component in enumerate(['X', 'Y', 'Z']):
            axes[i].plot(time_steps, observations[:n_samples_plot, i])
            axes[i].set_ylabel(f'Accel {component}')
            axes[i].grid(True)
        
        # Plot activity states
        activity_numeric = [self.activities.index(act) for act in activity_sequence[:n_samples_plot]]
        axes[3].plot(time_steps, activity_numeric, 'o-', markersize=3)
        axes[3].set_ylabel('Activity')
        axes[3].set_xlabel('Time Step')
        axes[3].set_yticks([0, 1, 2])
        axes[3].set_yticklabels(['Sitting', 'Walking', 'Running'])
        axes[3].grid(True)
        
        plt.tight_layout()
        plt.savefig('data_visualization.png', dpi=150, bbox_inches='tight')
        plt.show()

def main():
    """Generate and save sample data"""
    
    # Create data directory
    os.makedirs('data', exist_ok=True)
    
    # Generate data
    generator = ActivityDataGenerator(random_seed=42)
    
    # Generate training data (longer sequence)
    print("Generating training data...")
    train_obs, train_activities, train_states = generator.generate_sequence(n_samples=2000)
    train_df = generator.save_data(train_obs, train_activities, train_states, 
                                   'data/train_data.csv')
    
    # Generate test data
    print("Generating test data...")
    test_obs, test_activities, test_states = generator.generate_sequence(n_samples=500)
    test_df = generator.save_data(test_obs, test_activities, test_states, 
                                  'data/test_data.csv')
    
    # Plot sample of training data
    print("Creating data visualization...")
    generator.plot_data(train_obs, train_activities, n_samples_plot=200)
    
    # Print data statistics
    print("\nData Statistics:")
    print(f"Training samples: {len(train_df)}")
    print(f"Test samples: {len(test_df)}")
    print(f"Activities distribution (training):")
    print(train_df['activity'].value_counts())
    
    return train_df, test_df

if __name__ == "__main__":
    train_df, test_df = main()
