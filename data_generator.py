"""
Data Loader for Human Activity Recognition HMM Project
Loads and preprocesses UCI HAR dataset from Kaggle
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Tuple, List
import os
import urllib.request
import zipfile
from sklearn.preprocessing import StandardScaler

class UCIHARDataLoader:
    """Load and preprocess UCI Human Activity Recognition dataset"""

    def __init__(self, data_dir='data'):
        self.data_dir = data_dir
        self.dataset_url = "https://archive.ics.uci.edu/ml/machine-learning-databases/00240/UCI%20HAR%20Dataset.zip"
        self.dataset_path = os.path.join(data_dir, "UCI_HAR_Dataset.zip")
        self.extracted_path = os.path.join(data_dir, "UCI HAR Dataset")

        # Activity labels mapping
        self.activity_labels = {
            1: 'WALKING',
            2: 'WALKING_UPSTAIRS',
            3: 'WALKING_DOWNSTAIRS',
            4: 'SITTING',
            5: 'STANDING',
            6: 'LAYING'
        }

        # For HMM, we'll focus on main activities
        self.simplified_activities = {
            'WALKING': 0,
            'WALKING_UPSTAIRS': 0,  # Group as walking
            'WALKING_DOWNSTAIRS': 0,  # Group as walking
            'SITTING': 1,
            'STANDING': 1,  # Group as sitting/stationary
            'LAYING': 2
        }
        
    def generate_sequence(self, n_samples: int = 1000) -> Tuple[np.ndarray, List[str], List[int]]:
        """
        Generate a sequence of accelerometer readings with hidden activity states
        
        Args:
            n_samples: Number of time steps to generate
            
        Returns:
            observations: (n_samples, 3) array of [accel_x, accel_y, accel_z]
            activity_sequence: List of activity names
            state_sequence: List of state indices (0=sitting, 1=walking, 2=running)
        """
        
        observations = np.zeros((n_samples, 3))
        activity_sequence = []
        state_sequence = []
        
        # Start with random activity
        current_state = np.random.choice(3)
        
        for t in range(n_samples):
            # Record current state
            current_activity = self.activities[current_state]
            activity_sequence.append(current_activity)
            state_sequence.append(current_state)
            
            # Generate observation based on current activity
            params = self.activity_params[current_activity]
            observation = np.random.normal(params['mean'], params['std'])
            observations[t] = observation
            
            # Transition to next state
            if t < n_samples - 1:
                current_state = np.random.choice(3, p=self.transition_matrix[current_state])
        
        return observations, activity_sequence, state_sequence
    
    def save_data(self, observations: np.ndarray, activity_sequence: List[str], 
                  state_sequence: List[int], filename: str = 'activity_data.csv'):
        """Save generated data to CSV file"""
        
        df = pd.DataFrame({
            'timestamp': range(len(observations)),
            'accel_x': observations[:, 0],
            'accel_y': observations[:, 1], 
            'accel_z': observations[:, 2],
            'activity': activity_sequence,
            'state_id': state_sequence
        })
        
        df.to_csv(filename, index=False)
        print(f"Data saved to {filename}")
        return df
    
    def plot_data(self, observations: np.ndarray, activity_sequence: List[str], 
                  n_samples_plot: int = 200):
        """Plot the generated accelerometer data"""
        
        fig, axes = plt.subplots(4, 1, figsize=(12, 10))
        
        time_steps = range(min(n_samples_plot, len(observations)))
        
        # Plot accelerometer components
        for i, component in enumerate(['X', 'Y', 'Z']):
            axes[i].plot(time_steps, observations[:n_samples_plot, i])
            axes[i].set_ylabel(f'Accel {component}')
            axes[i].grid(True)
        
        # Plot activity states
        activity_numeric = [self.activities.index(act) for act in activity_sequence[:n_samples_plot]]
        axes[3].plot(time_steps, activity_numeric, 'o-', markersize=3)
        axes[3].set_ylabel('Activity')
        axes[3].set_xlabel('Time Step')
        axes[3].set_yticks([0, 1, 2])
        axes[3].set_yticklabels(['Sitting', 'Walking', 'Running'])
        axes[3].grid(True)
        
        plt.tight_layout()
        plt.savefig('data_visualization.png', dpi=150, bbox_inches='tight')
        plt.show()

def main():
    """Generate and save sample data"""
    
    # Create data directory
    os.makedirs('data', exist_ok=True)
    
    # Generate data
    generator = ActivityDataGenerator(random_seed=42)
    
    # Generate training data (longer sequence)
    print("Generating training data...")
    train_obs, train_activities, train_states = generator.generate_sequence(n_samples=2000)
    train_df = generator.save_data(train_obs, train_activities, train_states, 
                                   'data/train_data.csv')
    
    # Generate test data
    print("Generating test data...")
    test_obs, test_activities, test_states = generator.generate_sequence(n_samples=500)
    test_df = generator.save_data(test_obs, test_activities, test_states, 
                                  'data/test_data.csv')
    
    # Plot sample of training data
    print("Creating data visualization...")
    generator.plot_data(train_obs, train_activities, n_samples_plot=200)
    
    # Print data statistics
    print("\nData Statistics:")
    print(f"Training samples: {len(train_df)}")
    print(f"Test samples: {len(test_df)}")
    print(f"Activities distribution (training):")
    print(train_df['activity'].value_counts())
    
    return train_df, test_df

if __name__ == "__main__":
    train_df, test_df = main()
