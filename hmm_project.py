"""
Complete HMM Human Activity Recognition Project
CS280 Fall 2025 - SMU MedTech

This file contains the complete implementation for the HMM project.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from hmmlearn import hmm
import warnings
warnings.filterwarnings('ignore')

class UCIHARLoader:
    """Load and preprocess UCI HAR dataset"""
    
    def __init__(self, data_dir='archive'):
        self.data_dir = data_dir
        self.activity_mapping = {
            'WALKING': 0, 'WALKING_UPSTAIRS': 0, 'WALKING_DOWNSTAIRS': 0,
            'SITTING': 1, 'STANDING': 1, 'LAYING': 2
        }
        self.activity_names = ['Walking', 'Stationary', 'Laying']
    
    def load_and_preprocess(self, n_features=15):
        """Load and preprocess the complete dataset"""
        
        # Load data
        train_df = pd.read_csv(f'{self.data_dir}/train.csv')
        test_df = pd.read_csv(f'{self.data_dir}/test.csv')
        
        # Extract features and labels
        feature_cols = [col for col in train_df.columns if col not in ['subject', 'Activity']]
        
        train_X = train_df[feature_cols].values[:, :n_features]  # Use first n_features
        test_X = test_df[feature_cols].values[:, :n_features]
        
        # Map activities to simplified states
        train_states = np.array([self.activity_mapping[act] for act in train_df['Activity']])
        test_states = np.array([self.activity_mapping[act] for act in test_df['Activity']])
        
        # Standardize features
        mean = np.mean(train_X, axis=0)
        std = np.std(train_X, axis=0)
        train_X = (train_X - mean) / (std + 1e-8)
        test_X = (test_X - mean) / (std + 1e-8)
        
        print(f"Loaded UCI HAR dataset:")
        print(f"  Training: {train_X.shape[0]} samples, {train_X.shape[1]} features")
        print(f"  Test: {test_X.shape[0]} samples, {test_X.shape[1]} features")
        print(f"  States: {self.activity_names}")
        
        return train_X, train_states, test_X, test_states

class ActivityHMM:
    """Hidden Markov Model for Activity Recognition"""
    
    def __init__(self, n_states=3, random_state=42):
        self.n_states = n_states
        self.random_state = random_state
        self.model = None
        self.is_trained = False
        
    def train(self, observations, n_iter=100):
        """Train the HMM using Baum-Welch algorithm"""
        
        self.model = hmm.GaussianHMM(
            n_components=self.n_states,
            covariance_type="full",
            n_iter=n_iter,
            random_state=self.random_state,
            verbose=False
        )
        
        print(f"Training HMM with {self.n_states} states...")
        self.model.fit(observations)
        self.is_trained = True
        
        score = self.model.score(observations)
        print(f"Training completed. Log-likelihood: {score:.2f}")
        
    def predict_states(self, observations):
        """Predict most likely state sequence using Viterbi"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        log_prob, state_sequence = self.model.decode(observations, algorithm="viterbi")
        return state_sequence
    
    def evaluate(self, observations, true_states):
        """Evaluate model performance"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        
        predicted_states = self.predict_states(observations)
        accuracy = np.mean(true_states == predicted_states)
        log_likelihood = self.model.score(observations)
        
        # Calculate AIC and BIC
        n_params = (self.n_states * (self.n_states - 1) + 
                   self.n_states * observations.shape[1] + 
                   self.n_states * observations.shape[1] * (observations.shape[1] + 1) / 2)
        
        aic = 2 * n_params - 2 * log_likelihood
        bic = np.log(len(observations)) * n_params - 2 * log_likelihood
        
        return {
            'accuracy': accuracy,
            'log_likelihood': log_likelihood,
            'aic': aic,
            'bic': bic,
            'predicted_states': predicted_states
        }
    
    def forecast_next_states(self, observations, n_steps=5):
        """Forecast next n states"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        
        # Get current state probabilities
        state_probs = self.model.predict_proba(observations)
        current_state_prob = state_probs[-1]
        
        forecasted_states = []
        for _ in range(n_steps):
            next_state_prob = current_state_prob @ self.model.transmat_
            next_state = np.argmax(next_state_prob)
            forecasted_states.append(next_state)
            
            # Update for next iteration
            current_state_prob = np.zeros(self.n_states)
            current_state_prob[next_state] = 1.0
        
        return forecasted_states

def compare_models(train_X, train_states, n_states_list=[2, 3, 4]):
    """Compare HMM models with different numbers of states"""
    
    results = []
    models = {}
    
    for n_states in n_states_list:
        print(f"\nTraining model with {n_states} states...")
        
        model = ActivityHMM(n_states=n_states)
        model.train(train_X)
        
        eval_results = model.evaluate(train_X, train_states)
        eval_results['n_states'] = n_states
        results.append(eval_results)
        models[n_states] = model
        
        print(f"  Accuracy: {eval_results['accuracy']:.3f}")
        print(f"  BIC: {eval_results['bic']:.2f}")
    
    return pd.DataFrame(results), models

def plot_results(results_df, best_model, test_X, test_states):
    """Create visualizations"""
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # Model comparison
    axes[0,0].plot(results_df['n_states'], results_df['accuracy'], 'o-')
    axes[0,0].set_title('Accuracy vs Number of States')
    axes[0,0].set_xlabel('Number of States')
    axes[0,0].set_ylabel('Accuracy')
    axes[0,0].grid(True)
    
    axes[0,1].plot(results_df['n_states'], results_df['bic'], 'o-')
    axes[0,1].set_title('BIC vs Number of States')
    axes[0,1].set_xlabel('Number of States')
    axes[0,1].set_ylabel('BIC')
    axes[0,1].grid(True)
    
    # Transition matrix
    sns.heatmap(best_model.model.transmat_, annot=True, fmt='.3f', 
               xticklabels=['Walking', 'Stationary', 'Laying'],
               yticklabels=['Walking', 'Stationary', 'Laying'],
               ax=axes[1,0], cmap='Blues')
    axes[1,0].set_title('Transition Matrix')
    
    # State sequence sample
    sample_size = 200
    predicted_states = best_model.predict_states(test_X[:sample_size])
    
    axes[1,1].plot(range(sample_size), test_states[:sample_size], 'g-', label='True', alpha=0.7)
    axes[1,1].plot(range(sample_size), predicted_states, 'r--', label='Predicted', alpha=0.7)
    axes[1,1].set_title('State Sequence Prediction')
    axes[1,1].set_xlabel('Time Step')
    axes[1,1].set_ylabel('State')
    axes[1,1].legend()
    axes[1,1].grid(True)
    
    plt.tight_layout()
    plt.savefig('hmm_results.png', dpi=150, bbox_inches='tight')
    plt.show()

def main():
    """Run complete HMM project demo"""
    
    print("=" * 60)
    print("HMM HUMAN ACTIVITY RECOGNITION PROJECT")
    print("CS280 Fall 2025 - SMU MedTech")
    print("=" * 60)
    
    # 1. Load data
    print("\n1. LOADING UCI HAR DATASET")
    loader = UCIHARLoader()
    train_X, train_states, test_X, test_states = loader.load_and_preprocess(n_features=15)
    
    # Use subset for demo
    n_train, n_test = 2000, 500
    train_X = train_X[:n_train]
    train_states = train_states[:n_train]
    test_X = test_X[:n_test]
    test_states = test_states[:n_test]
    
    print(f"Using subset: {n_train} train, {n_test} test samples")
    
    # 2. Model comparison
    print("\n2. MODEL COMPARISON")
    results_df, models = compare_models(train_X, train_states)
    
    # 3. Select best model
    print("\n3. MODEL SELECTION")
    best_idx = np.argmin(results_df['bic'])
    best_n_states = results_df.iloc[best_idx]['n_states']
    best_model = models[best_n_states]
    
    print(f"Best model: {best_n_states} states (BIC: {results_df.iloc[best_idx]['bic']:.2f})")
    
    # 4. Test evaluation
    print("\n4. TEST EVALUATION")
    test_results = best_model.evaluate(test_X, test_states)
    print(f"Test Accuracy: {test_results['accuracy']:.3f}")
    print(f"Test Log-likelihood: {test_results['log_likelihood']:.2f}")
    
    # 5. Forecasting demo
    print("\n5. FORECASTING DEMO")
    context_length = 20
    forecast_steps = 10
    
    context_obs = test_X[:context_length]
    true_future = test_states[context_length:context_length + forecast_steps]
    predicted_future = best_model.forecast_next_states(context_obs, n_steps=forecast_steps)
    
    print(f"True future states: {true_future}")
    print(f"Predicted states:   {predicted_future}")
    
    forecast_accuracy = np.mean(true_future == predicted_future)
    print(f"Forecast accuracy: {forecast_accuracy:.3f}")
    
    # 6. Visualizations
    print("\n6. CREATING VISUALIZATIONS")
    plot_results(results_df, best_model, test_X, test_states)
    
    # 7. Summary
    print("\n7. PROJECT SUMMARY")
    print(f"Dataset: UCI HAR (simplified to 3 states)")
    print(f"Best model: {best_n_states} states")
    print(f"Test accuracy: {test_results['accuracy']:.3f}")
    print(f"Forecast accuracy: {forecast_accuracy:.3f}")
    print("\nKey findings:")
    print("- HMM successfully learns activity patterns from sensor data")
    print("- Model distinguishes between walking, stationary, and laying")
    print("- Forecasting demonstrates predictive capability")
    
    print("\n" + "=" * 60)
    print("PROJECT COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    
    return best_model, results_df, test_results

if __name__ == "__main__":
    model, results, test_results = main()
