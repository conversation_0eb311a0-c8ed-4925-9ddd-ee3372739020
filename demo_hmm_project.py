"""
Complete HMM Activity Recognition Demo
Demonstrates the full pipeline from data loading to model evaluation
"""

import numpy as np
import matplotlib.pyplot as plt
from data_generator import UCIHARDataLoader
from hmm_activity_recognition import ActivityHMM, compare_models

def main():
    """Run complete HMM demo"""
    
    print("=" * 60)
    print("HMM HUMAN ACTIVITY RECOGNITION DEMO")
    print("=" * 60)
    
    # 1. Load and preprocess data
    print("\n1. LOADING UCI HAR DATASET")
    print("-" * 30)
    
    loader = UCIHARDataLoader(data_dir='archive')
    train_df, test_df = loader.load_data()
    
    # Preprocess data
    train_X, train_activities, train_states = loader.preprocess_data(train_df, fit_scaler=True)
    test_X, test_activities, test_states = loader.preprocess_data(test_df, fit_scaler=False)
    
    # Select key features
    train_obs = loader.select_key_features(train_X, n_features=15)
    test_obs = loader.select_key_features(test_X, n_features=15)
    
    print(f"Training samples: {len(train_obs)}")
    print(f"Test samples: {len(test_obs)}")
    print(f"Features: {train_obs.shape[1]}")
    print(f"States: {loader.activity_names}")
    
    # 2. Model comparison
    print("\n2. MODEL COMPARISON")
    print("-" * 30)
    
    # Use subset for faster demo
    n_train = 2000
    n_test = 500
    
    train_subset = train_obs[:n_train]
    train_states_subset = train_states[:n_train]
    test_subset = test_obs[:n_test]
    test_states_subset = test_states[:n_test]
    
    print(f"Using subset for demo: {n_train} train, {n_test} test samples")
    
    # Compare models with different numbers of states
    results_df, models = compare_models(
        train_subset, train_states_subset, 
        n_states_list=[2, 3, 4]
    )
    
    # 3. Select best model
    print("\n3. MODEL SELECTION")
    print("-" * 30)
    
    best_model_idx = np.argmin(results_df['bic'])
    best_n_states = results_df.iloc[best_model_idx]['n_states']
    best_model = models[best_n_states]
    
    print(f"Best model: {best_n_states} states")
    print(f"Best BIC: {results_df.iloc[best_model_idx]['bic']:.2f}")
    print(f"Best accuracy: {results_df.iloc[best_model_idx]['accuracy']:.3f}")
    
    # 4. Detailed evaluation
    print("\n4. DETAILED EVALUATION")
    print("-" * 30)
    
    test_results = best_model.evaluate(test_subset, test_states_subset)
    
    print(f"Test Accuracy: {test_results['accuracy']:.3f}")
    print(f"Test Log-likelihood: {test_results['log_likelihood']:.2f}")
    
    # 5. Forecasting demo
    print("\n5. FORECASTING DEMO")
    print("-" * 30)
    
    # Use first 20 samples as context
    context_length = 20
    forecast_steps = 10
    
    context_obs = test_subset[:context_length]
    true_future_states = test_states_subset[context_length:context_length + forecast_steps]
    
    # HMM forecast
    forecast_obs, forecast_states = best_model.forecast_next_observation(
        context_obs, n_steps=forecast_steps
    )
    
    print(f"Context length: {context_length}")
    print(f"Forecasting {forecast_steps} steps ahead")
    print(f"True future states: {true_future_states}")
    print(f"Predicted states: {forecast_states}")
    
    # Calculate forecast accuracy
    forecast_accuracy = np.mean(true_future_states == forecast_states)
    print(f"Forecast accuracy: {forecast_accuracy:.3f}")
    
    # 6. Visualize results
    print("\n6. CREATING VISUALIZATIONS")
    print("-" * 30)
    
    # Plot model parameters
    best_model.plot_model_parameters()
    
    # Plot state sequence
    best_model.plot_state_sequence(test_subset[:200], test_states_subset[:200], n_samples=200)
    
    # 7. Summary
    print("\n7. PROJECT SUMMARY")
    print("-" * 30)
    print(f"Dataset: UCI Human Activity Recognition")
    print(f"Original activities: 6 (WALKING, SITTING, STANDING, etc.)")
    print(f"Simplified states: 3 (Walking, Stationary, Laying)")
    print(f"Features: {train_obs.shape[1]} (selected from 561 original)")
    print(f"Training samples: {len(train_obs)}")
    print(f"Test samples: {len(test_obs)}")
    print()
    print(f"Best HMM model: {best_n_states} states")
    print(f"Test accuracy: {test_results['accuracy']:.3f}")
    print(f"Forecast accuracy: {forecast_accuracy:.3f}")
    print()
    print("Key findings:")
    print("- HMM successfully learns activity patterns from sensor data")
    print("- Model can distinguish between walking, stationary, and laying activities")
    print("- Forecasting capability enables prediction of future activities")
    print("- Real-world dataset demonstrates practical applicability")
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    
    return best_model, results_df, test_results

if __name__ == "__main__":
    model, comparison_results, test_results = main()
