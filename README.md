# Hidden Markov Models for Human Activity Recognition

**CS280 Project - Fall 2025**  
**SMU MedTech Program**

## Project Overview

This project implements a Hidden Markov Model (HMM) for human activity recognition using the UCI Human Activity Recognition dataset. The model classifies smartphone sensor data into three main activity states: Walking, Stationary (sitting/standing), and Laying.

## Dataset

- **Source**: UCI Human Activity Recognition Dataset (Kaggle)
- **Original Activities**: 6 types (WALKING, WALKING_UPSTAIRS, WALKING_DOWNSTAIRS, SITTING, STANDING, LAYING)
- **Simplified States**: 3 types (Walking, Stationary, Laying)
- **Features**: 561 time and frequency domain features → reduced to 15-20 key features
- **Samples**: 7,352 training + 2,947 test samples
- **Subjects**: 30 volunteers aged 19-48

## Project Structure

```
├── README.md                          # This file
├── requirements.txt                   # Python dependencies
├── data_generator.py                  # UCI HAR data loader and preprocessor
├── hmm_activity_recognition.py        # HMM implementation and evaluation
├── HMM_Activity_Recognition.ipynb     # Complete Jupyter notebook
├── demo_hmm_project.py               # Full demo script
├── test_hmm.py                       # Quick test script
├── archive/                          # UCI HAR dataset
│   ├── train.csv
│   └── test.csv
└── data/                             # Processed data
    ├── train_X.npy
    ├── train_states.npy
    ├── test_X.npy
    └── test_states.npy
```

## Key Features

### 1. Data Processing
- Loads UCI HAR dataset from CSV files
- Maps 6 original activities to 3 simplified states
- Feature selection (20 most relevant features from 561)
- Standardization and preprocessing

### 2. HMM Implementation
- **Training**: Baum-Welch algorithm (EM) using hmmlearn
- **Inference**: Forward-Backward algorithm for state probabilities
- **Decoding**: Viterbi algorithm for most likely state sequence
- **Forecasting**: Multi-step ahead prediction

### 3. Model Selection
- Compares models with 2, 3, 4, and 5 states
- Uses AIC/BIC for model selection
- Cross-validation and evaluation metrics

### 4. Evaluation
- Accuracy, precision, recall, F1-score
- Confusion matrices
- Log-likelihood analysis
- Forecasting performance vs. naive baseline

## Installation and Setup

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Download UCI HAR dataset**:
   - Download from [Kaggle UCI HAR Dataset](https://www.kaggle.com/datasets/uciml/human-activity-recognition-with-smartphones)
   - Extract `train.csv` and `test.csv` to `archive/` folder

3. **Run the project**:
   ```bash
   # Quick test
   python test_hmm.py
   
   # Full demo
   python demo_hmm_project.py
   
   # Process data
   python data_generator.py
   
   # Jupyter notebook
   jupyter notebook HMM_Activity_Recognition.ipynb
   ```

## Results Summary

### Model Performance
- **Best Model**: 3-4 states (selected by BIC)
- **Test Accuracy**: ~50-70% (varies by subset and parameters)
- **Log-likelihood**: Converges successfully
- **Forecasting**: Demonstrates predictive capability

### Key Findings
1. **HMM Effectiveness**: Successfully learns activity patterns from sensor data
2. **State Interpretation**: Model distinguishes between walking, stationary, and laying activities
3. **Real-world Applicability**: Works with actual smartphone sensor data
4. **Forecasting Capability**: Can predict future activities based on current context

## Technical Implementation

### HMM Components
- **Hidden States**: S = {0: Walking, 1: Stationary, 2: Laying}
- **Observations**: 15-20 dimensional feature vectors
- **Emission Model**: Multivariate Gaussian distributions
- **Parameters**: Initial probabilities π, transition matrix A, emission parameters (μ, Σ)

### Algorithms Used
- **Training**: Expectation-Maximization (Baum-Welch)
- **Inference**: Forward-Backward algorithm
- **Decoding**: Viterbi algorithm
- **Model Selection**: AIC/BIC comparison

### Assumptions
- **Markov Property**: Current activity depends only on previous activity
- **Observation Independence**: Sensor readings are independent given the activity
- **Gaussian Emissions**: Feature vectors follow multivariate normal distributions
- **Stationary Process**: Activity patterns don't change over time

## Files Description

- **`data_generator.py`**: Loads and preprocesses UCI HAR dataset
- **`hmm_activity_recognition.py`**: Core HMM implementation with training and evaluation
- **`demo_hmm_project.py`**: Complete demonstration of the entire pipeline
- **`test_hmm.py`**: Quick test script for verification
- **`HMM_Activity_Recognition.ipynb`**: Comprehensive Jupyter notebook with analysis

## Future Improvements

1. **Enhanced Features**: Use raw sensor data instead of pre-computed features
2. **More Activities**: Include more complex activities (stairs, cycling, etc.)
3. **Online Learning**: Implement adaptive models for changing patterns
4. **Deep Learning**: Compare with LSTM/RNN approaches
5. **Real-time Processing**: Optimize for mobile deployment

## References

1. UCI Human Activity Recognition Dataset
2. Rabiner, L. R. (1989). A tutorial on hidden Markov models and selected applications in speech recognition
3. hmmlearn Python library documentation

## Authors

**CS280 Fall 2025 Project**  
SMU MedTech Program

---

*This project demonstrates the practical application of Hidden Markov Models to real-world human activity recognition using smartphone sensor data.*
