"""
Test script for HMM with UCI HAR dataset
"""

import numpy as np
from hmm_activity_recognition import ActivityHMM

def test_hmm():
    """Test HMM implementation with real data"""
    
    print("Loading processed data...")
    
    # Load the processed data
    train_X = np.load('data/train_X.npy')
    train_states = np.load('data/train_states.npy')
    test_X = np.load('data/test_X.npy')
    test_states = np.load('data/test_states.npy')
    
    print(f"Training data: {train_X.shape}")
    print(f"Test data: {test_X.shape}")
    print(f"Number of states: {len(np.unique(train_states))}")
    
    # Use a subset for quick testing
    n_train = 1000
    n_test = 200
    
    train_X_subset = train_X[:n_train]
    train_states_subset = train_states[:n_train]
    test_X_subset = test_X[:n_test]
    test_states_subset = test_states[:n_test]
    
    print(f"\nUsing subset for testing:")
    print(f"Training subset: {train_X_subset.shape}")
    print(f"Test subset: {test_X_subset.shape}")
    
    # Test HMM with 3 states
    print("\nTesting HMM with 3 states...")
    hmm_model = ActivityHMM(n_states=3, random_state=42)
    
    # Train the model
    print("Training HMM...")
    hmm_model.train(train_X_subset, n_iter=50)
    
    # Evaluate on test data
    print("Evaluating on test data...")
    results = hmm_model.evaluate(test_X_subset, test_states_subset)
    
    print(f"\nResults:")
    print(f"Accuracy: {results['accuracy']:.3f}")
    print(f"Log-likelihood: {results['log_likelihood']:.2f}")
    print(f"AIC: {results['aic']:.2f}")
    print(f"BIC: {results['bic']:.2f}")
    
    # Test forecasting
    print("\nTesting forecasting...")
    context_obs = test_X_subset[:10]
    forecast_obs, forecast_states = hmm_model.forecast_next_observation(context_obs, n_steps=5)
    
    print(f"Forecasted {len(forecast_states)} steps")
    print(f"Forecasted states: {forecast_states}")
    
    print("\nHMM test completed successfully!")
    
    return hmm_model, results

if __name__ == "__main__":
    model, results = test_hmm()
