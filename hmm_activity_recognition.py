import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from hmmlearn import hmm
import warnings
warnings.filterwarnings('ignore')

class ActivityHMM:
    """Hidden Markov Model for Activity Recognition"""
    
    def __init__(self, n_states=3, random_state=42):
        self.n_states = n_states
        self.random_state = random_state
        self.model = None
        self.activity_names = ['sitting', 'walking', 'running']
        self.is_trained = False
        
    def train(self, observations, n_iter=100):
        """
        Train the HMM using Baum-Welch algorithm
        
        Args:
            observations: (n_samples, n_features) array of accelerometer data
            n_iter: Maximum number of EM iterations
        """
        
        # Create Gaussian HMM model
        self.model = hmm.GaussianHMM(
            n_components=self.n_states,
            covariance_type="full",
            n_iter=n_iter,
            random_state=self.random_state,
            verbose=False
        )
        
        # Fit the model
        print(f"Training HMM with {self.n_states} states...")
        self.model.fit(observations)
        self.is_trained = True
        
        # Print convergence info
        print(f"Training completed. Final log-likelihood: {self.model.score(observations):.2f}")
        print(f"Converged: {self.model.monitor_.converged}")
        
    def predict_states(self, observations):
        """Predict most likely state sequence using Viterbi algorithm"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
            
        log_prob, state_sequence = self.model.decode(observations, algorithm="viterbi")
        return state_sequence, log_prob
    
    def predict_proba(self, observations):
        """Compute state probabilities using Forward-Backward algorithm"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
            
        return self.model.predict_proba(observations)
    
    def forecast_next_observation(self, observations, n_steps=1):
        """
        Forecast next observation(s) based on current sequence
        
        Args:
            observations: Current observation sequence
            n_steps: Number of steps to forecast
            
        Returns:
            forecasted_obs: Predicted observations
            forecasted_states: Predicted states
        """
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        
        # Get current state probabilities
        state_probs = self.model.predict_proba(observations)
        current_state_prob = state_probs[-1]  # Last time step
        
        forecasted_obs = []
        forecasted_states = []
        
        for step in range(n_steps):
            # Predict next state distribution
            next_state_prob = current_state_prob @ self.model.transmat_
            
            # Sample next state
            next_state = np.random.choice(self.n_states, p=next_state_prob)
            forecasted_states.append(next_state)
            
            # Sample next observation from emission distribution
            mean = self.model.means_[next_state]
            cov = self.model.covars_[next_state]
            next_obs = np.random.multivariate_normal(mean, cov)
            forecasted_obs.append(next_obs)
            
            # Update for next iteration
            current_state_prob = np.zeros(self.n_states)
            current_state_prob[next_state] = 1.0
        
        return np.array(forecasted_obs), forecasted_states
    
    def evaluate(self, observations, true_states):
        """Evaluate model performance"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        
        # Predict states
        predicted_states, log_prob = self.predict_states(observations)
        
        # Calculate metrics
        accuracy = accuracy_score(true_states, predicted_states)
        
        # Calculate log-likelihood
        log_likelihood = self.model.score(observations)
        
        # Calculate AIC and BIC
        n_params = (self.n_states * (self.n_states - 1) +  # transition matrix
                   self.n_states * observations.shape[1] +    # means
                   self.n_states * observations.shape[1] * (observations.shape[1] + 1) / 2)  # covariances
        
        aic = 2 * n_params - 2 * log_likelihood
        bic = np.log(len(observations)) * n_params - 2 * log_likelihood
        
        results = {
            'accuracy': accuracy,
            'log_likelihood': log_likelihood,
            'aic': aic,
            'bic': bic,
            'predicted_states': predicted_states,
            'true_states': true_states
        }
        
        return results
    
    def plot_model_parameters(self):
        """Visualize learned model parameters"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Plot transition matrix
        sns.heatmap(self.model.transmat_, annot=True, fmt='.3f', 
                   xticklabels=[f'State {i}' for i in range(self.n_states)],
                   yticklabels=[f'State {i}' for i in range(self.n_states)],
                   ax=axes[0,0], cmap='Blues')
        axes[0,0].set_title('Transition Matrix')
        
        # Plot emission means
        means_df = pd.DataFrame(self.model.means_, 
                               columns=['Accel_X', 'Accel_Y', 'Accel_Z'],
                               index=[f'State {i}' for i in range(self.n_states)])
        sns.heatmap(means_df, annot=True, fmt='.2f', ax=axes[0,1], cmap='RdYlBu')
        axes[0,1].set_title('Emission Means')
        
        # Plot initial state probabilities
        axes[1,0].bar(range(self.n_states), self.model.startprob_)
        axes[1,0].set_title('Initial State Probabilities')
        axes[1,0].set_xlabel('State')
        axes[1,0].set_ylabel('Probability')
        axes[1,0].set_xticks(range(self.n_states))
        
        # Plot emission standard deviations (diagonal of covariance)
        stds = np.sqrt(np.diagonal(self.model.covars_, axis1=1, axis2=2))
        stds_df = pd.DataFrame(stds,
                              columns=['Accel_X', 'Accel_Y', 'Accel_Z'],
                              index=[f'State {i}' for i in range(self.n_states)])
        sns.heatmap(stds_df, annot=True, fmt='.2f', ax=axes[1,1], cmap='Oranges')
        axes[1,1].set_title('Emission Standard Deviations')
        
        plt.tight_layout()
        plt.savefig('hmm_parameters.png', dpi=150, bbox_inches='tight')
        plt.show()
    
    def plot_state_sequence(self, observations, true_states=None, n_samples=200):
        """Plot state sequence and observations"""
        if not self.is_trained:
            raise ValueError("Model must be trained first")
        
        # Predict states and probabilities
        predicted_states, _ = self.predict_states(observations)
        state_probs = self.predict_proba(observations)
        
        n_plot = min(n_samples, len(observations))
        time_steps = range(n_plot)
        
        fig, axes = plt.subplots(5, 1, figsize=(15, 12))
        
        # Plot accelerometer data
        for i, component in enumerate(['X', 'Y', 'Z']):
            axes[i].plot(time_steps, observations[:n_plot, i], alpha=0.7)
            axes[i].set_ylabel(f'Accel {component}')
            axes[i].grid(True)
        
        # Plot true states if available
        if true_states is not None:
            axes[3].plot(time_steps, true_states[:n_plot], 'g-', label='True States', linewidth=2)
        
        # Plot predicted states
        axes[3].plot(time_steps, predicted_states[:n_plot], 'r--', label='Predicted States', linewidth=2)
        axes[3].set_ylabel('State')
        axes[3].set_ylim(-0.5, self.n_states - 0.5)
        axes[3].legend()
        axes[3].grid(True)
        
        # Plot state probabilities
        for state in range(self.n_states):
            axes[4].plot(time_steps, state_probs[:n_plot, state], 
                        label=f'State {state}', alpha=0.8)
        axes[4].set_ylabel('State Probability')
        axes[4].set_xlabel('Time Step')
        axes[4].legend()
        axes[4].grid(True)
        
        plt.tight_layout()
        plt.savefig('state_sequence.png', dpi=150, bbox_inches='tight')
        plt.show()

def naive_baseline_forecast(observations, n_steps=1):
    """Simple baseline: predict next observation as mean of last few observations"""
    window_size = min(10, len(observations))
    recent_obs = observations[-window_size:]
    mean_obs = np.mean(recent_obs, axis=0)
    
    # Return the same prediction for all steps
    return np.tile(mean_obs, (n_steps, 1))

def compare_models(observations, true_states, n_states_list=[2, 3, 4, 5]):
    """Compare HMM models with different numbers of states"""
    
    results = []
    models = {}
    
    for n_states in n_states_list:
        print(f"\nTraining model with {n_states} states...")
        
        model = ActivityHMM(n_states=n_states)
        model.train(observations)
        
        eval_results = model.evaluate(observations, true_states)
        eval_results['n_states'] = n_states
        results.append(eval_results)
        models[n_states] = model
        
        print(f"Accuracy: {eval_results['accuracy']:.3f}")
        print(f"Log-likelihood: {eval_results['log_likelihood']:.2f}")
        print(f"AIC: {eval_results['aic']:.2f}")
        print(f"BIC: {eval_results['bic']:.2f}")
    
    # Create comparison plot
    results_df = pd.DataFrame(results)
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    axes[0,0].plot(results_df['n_states'], results_df['accuracy'], 'o-')
    axes[0,0].set_title('Accuracy vs Number of States')
    axes[0,0].set_xlabel('Number of States')
    axes[0,0].set_ylabel('Accuracy')
    axes[0,0].grid(True)
    
    axes[0,1].plot(results_df['n_states'], results_df['log_likelihood'], 'o-')
    axes[0,1].set_title('Log-Likelihood vs Number of States')
    axes[0,1].set_xlabel('Number of States')
    axes[0,1].set_ylabel('Log-Likelihood')
    axes[0,1].grid(True)
    
    axes[1,0].plot(results_df['n_states'], results_df['aic'], 'o-', label='AIC')
    axes[1,0].plot(results_df['n_states'], results_df['bic'], 's-', label='BIC')
    axes[1,0].set_title('Information Criteria vs Number of States')
    axes[1,0].set_xlabel('Number of States')
    axes[1,0].set_ylabel('IC Value')
    axes[1,0].legend()
    axes[1,0].grid(True)
    
    # Best model selection
    best_aic_idx = np.argmin(results_df['aic'])
    best_bic_idx = np.argmin(results_df['bic'])
    
    axes[1,1].text(0.1, 0.8, f"Best AIC: {results_df.iloc[best_aic_idx]['n_states']} states", 
                   transform=axes[1,1].transAxes, fontsize=12)
    axes[1,1].text(0.1, 0.6, f"Best BIC: {results_df.iloc[best_bic_idx]['n_states']} states", 
                   transform=axes[1,1].transAxes, fontsize=12)
    axes[1,1].text(0.1, 0.4, f"Best Accuracy: {results_df.iloc[np.argmax(results_df['accuracy'])]['n_states']} states", 
                   transform=axes[1,1].transAxes, fontsize=12)
    axes[1,1].set_title('Model Selection Summary')
    axes[1,1].axis('off')
    
    plt.tight_layout()
    plt.savefig('model_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return results_df, models
