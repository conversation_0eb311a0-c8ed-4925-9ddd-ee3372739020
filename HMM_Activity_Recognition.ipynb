{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Hidden Markov Models for Human Activity Recognition\n", "\n", "**CS280 Project - Fall 2025**\n", "\n", "## Project Overview\n", "This project implements a Hidden Markov Model (HMM) to recognize human activities from smartphone sensor data using the UCI Human Activity Recognition dataset. We classify activities into three main states: Walking, Stationary (sitting/standing), and Laying.\n", "\n", "### Problem Statement\n", "- **Hidden States**: Simplified human activities (Walking, Stationary, Laying)\n", "- **Observations**: 561-dimensional feature vectors from accelerometer and gyroscope data\n", "- **Dataset**: UCI HAR dataset with 7,352 training and 2,947 test samples\n", "- **Goal**: Learn activity patterns and predict future activities from sensor features"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import accuracy_score, classification_report, confusion_matrix\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import our custom modules\n", "from hmm_project import UCIHARLoader, ActivityHMM, compare_models\n", "\n", "# Set style for better plots\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Exploration\n", "\n", "We load the UCI Human Activity Recognition dataset and preprocess it for HMM training:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load UCI HAR dataset\n", "loader = UCIHARLoader(data_dir='archive')\n", "\n", "# Load and preprocess data\n", "print(\"Loading and preprocessing UCI HAR dataset...\")\n", "train_obs, train_states, test_obs, test_states = loader.load_and_preprocess(n_features=15)\n", "\n", "print(f\"Training samples: {len(train_obs)}\")\n", "print(f\"Test samples: {len(test_obs)}\")\n", "print(f\"Feature dimensions: {train_obs.shape[1]}\")\n", "print(f\"Activity states: {loader.activity_names}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the data\n", "loader.plot_data(train_obs, train_states, train_activities, n_samples_plot=500)\n", "\n", "# Show activity distribution\n", "print(\"\\nOriginal activity distribution (training):\")\n", "unique_activities, counts = np.unique(train_activities, return_counts=True)\n", "for activity, count in zip(unique_activities, counts):\n", "    print(f\"  {activity}: {count}\")\n", "\n", "print(\"\\nSimplified state distribution (training):\")\n", "unique_states, counts = np.unique(train_states, return_counts=True)\n", "for state, count in zip(unique_states, counts):\n", "    print(f\"  {loader.activity_names[state]}: {count}\")\n", "\n", "# Show basic statistics\n", "print(\"\\nFeature statistics (first 5 features):\")\n", "feature_stats = pd.DataFrame(train_obs[:, :5]).describe()\n", "print(feature_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. HMM Model Specification\n", "\n", "### Model Components:\n", "- **Hidden States**: S = {0: <PERSON>, 1: <PERSON><PERSON>, 2: Lay<PERSON>}\n", "- **Observations**: 20-dimensional feature vectors from accelerometer/gyroscope data\n", "- **Emission Model**: Multivariate Gaussian distributions\n", "- **Parameters**: \n", "  - Initial probabilities π\n", "  - Transition matrix A (3×3)\n", "  - Emission parameters (means μ and covariances Σ for each state)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Model Training and Selection\n", "\n", "We compare models with different numbers of states to find the optimal complexity:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare models with different numbers of states\n", "print(\"Comparing models with different numbers of states...\")\n", "results_df, models = compare_models(train_obs, train_states, n_states_list=[2, 3, 4, 5])\n", "\n", "# Display results table\n", "print(\"\\nModel Comparison Results:\")\n", "display_cols = ['n_states', 'accuracy', 'log_likelihood', 'aic', 'bic']\n", "print(results_df[display_cols].round(3))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Select best model based on BIC (balances fit and complexity)\n", "best_model_idx = np.argmin(results_df['bic'])\n", "best_n_states = results_df.iloc[best_model_idx]['n_states']\n", "best_model = models[best_n_states]\n", "\n", "print(f\"Best model selected: {best_n_states} states\")\n", "print(f\"Best model BIC: {results_df.iloc[best_model_idx]['bic']:.2f}\")\n", "print(f\"Best model accuracy: {results_df.iloc[best_model_idx]['accuracy']:.3f}\")\n", "\n", "# Visualize the best model parameters\n", "best_model.plot_model_parameters()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Model Evaluation and Inference\n", "\n", "Evaluate the best model on test data and analyze its performance:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate on test data\n", "test_results = best_model.evaluate(test_obs, test_states)\n", "\n", "print(\"Test Set Performance:\")\n", "print(f\"Accuracy: {test_results['accuracy']:.3f}\")\n", "print(f\"Log-likelihood: {test_results['log_likelihood']:.2f}\")\n", "\n", "# Classification report\n", "print(\"\\nClassification Report:\")\n", "activity_names = ['sitting', 'walking', 'running']\n", "if best_n_states == 3:\n", "    target_names = activity_names\n", "else:\n", "    target_names = [f'State {i}' for i in range(best_n_states)]\n", "\n", "print(classification_report(test_results['true_states'], \n", "                          test_results['predicted_states'],\n", "                          target_names=target_names))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion matrix\n", "cm = confusion_matrix(test_results['true_states'], test_results['predicted_states'])\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=target_names,\n", "            yticklabels=target_names)\n", "plt.title('Confusion Matrix - Test Set')\n", "plt.ylabel('True Activity')\n", "plt.xlabel('Predicted Activity')\n", "plt.tight_layout()\n", "plt.savefig('confusion_matrix.png', dpi=150, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize state sequence predictions\n", "best_model.plot_state_sequence(test_obs, test_states, n_samples=200)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Forecasting and Prediction\n", "\n", "Demonstrate the model's ability to forecast future observations and activities:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Forecasting experiment\n", "# Use first part of test sequence to predict next observations\n", "context_length = 50\n", "forecast_steps = 10\n", "\n", "context_obs = test_obs[:context_length]\n", "true_future_obs = test_obs[context_length:context_length + forecast_steps]\n", "true_future_states = test_states[context_length:context_length + forecast_steps]\n", "\n", "# HMM forecast\n", "hmm_forecast_obs, hmm_forecast_states = best_model.forecast_next_observation(\n", "    context_obs, n_steps=forecast_steps)\n", "\n", "# Naive baseline forecast\n", "baseline_forecast_obs = naive_baseline_forecast(context_obs, n_steps=forecast_steps)\n", "\n", "print(f\"Forecasting {forecast_steps} steps ahead...\")\n", "print(f\"Context length: {context_length} observations\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize forecasting results\n", "fig, axes = plt.subplots(4, 1, figsize=(12, 10))\n", "\n", "time_context = range(context_length)\n", "time_forecast = range(context_length, context_length + forecast_steps)\n", "\n", "# Plot accelerometer components\n", "for i, component in enumerate(['X', 'Y', 'Z']):\n", "    # Context (known)\n", "    axes[i].plot(time_context, context_obs[:, i], 'b-', label='Context', linewidth=2)\n", "    \n", "    # True future\n", "    axes[i].plot(time_forecast, true_future_obs[:, i], 'g-', label='True Future', linewidth=2)\n", "    \n", "    # HMM forecast\n", "    axes[i].plot(time_forecast, hmm_forecast_obs[:, i], 'r--', label='HMM Forecast', linewidth=2)\n", "    \n", "    # Baseline forecast\n", "    axes[i].plot(time_forecast, baseline_forecast_obs[:, i], 'orange', \n", "                linestyle=':', label='Baseline', linewidth=2)\n", "    \n", "    axes[i].axvline(x=context_length-0.5, color='black', linestyle='--', alpha=0.5)\n", "    axes[i].set_ylabel(f'Accel {component}')\n", "    axes[i].legend()\n", "    axes[i].grid(True)\n", "\n", "# Plot states\n", "axes[3].plot(time_context, [test_states[i] for i in time_context], 'b-', label='Context States', linewidth=2)\n", "axes[3].plot(time_forecast, true_future_states, 'g-', label='True Future States', linewidth=2)\n", "axes[3].plot(time_forecast, hmm_forecast_states, 'r--', label='HMM Forecast States', linewidth=2)\n", "axes[3].axvline(x=context_length-0.5, color='black', linestyle='--', alpha=0.5)\n", "axes[3].set_ylabel('Activity State')\n", "axes[3].set_xlabel('Time Step')\n", "axes[3].set_yticks([0, 1, 2])\n", "axes[3].set_yticklabels(['Sitting', 'Walking', 'Running'])\n", "axes[3].legend()\n", "axes[3].grid(True)\n", "\n", "plt.tight_layout()\n", "plt.savefig('forecasting_results.png', dpi=150, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate forecasting errors\n", "hmm_mse = np.mean((hmm_forecast_obs - true_future_obs) ** 2)\n", "baseline_mse = np.mean((baseline_forecast_obs - true_future_obs) ** 2)\n", "\n", "print(\"Forecasting Performance (MSE):\")\n", "print(f\"HMM Forecast MSE: {hmm_mse:.3f}\")\n", "print(f\"Baseline MSE: {baseline_mse:.3f}\")\n", "print(f\"Improvement: {((baseline_mse - hmm_mse) / baseline_mse * 100):.1f}%\")\n", "\n", "# State prediction accuracy\n", "state_accuracy = accuracy_score(true_future_states, hmm_forecast_states)\n", "print(f\"\\nState Forecasting Accuracy: {state_accuracy:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Results Summary and Discussion\n", "\n", "### Key Findings:\n", "1. **Model Selection**: The optimal number of states was determined using BIC\n", "2. **Performance**: The HMM achieved good accuracy in activity recognition\n", "3. **Forecasting**: The model can predict future activities better than naive baselines\n", "4. **Learned Parameters**: The model learned meaningful activity patterns\n", "\n", "### Model Assumptions:\n", "- **Markov Property**: Current activity depends only on previous activity\n", "- **Observation Independence**: Accelerometer readings are independent given the activity\n", "- **Gaussian Emissions**: Sensor readings follow multivariate normal distributions\n", "- **Stationary Process**: Activity patterns don't change over time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary statistics\n", "print(\"=\" * 50)\n", "print(\"FINAL PROJECT SUMMARY\")\n", "print(\"=\" * 50)\n", "print(f\"Dataset: Synthetic accelerometer data\")\n", "print(f\"Training samples: {len(train_obs)}\")\n", "print(f\"Test samples: {len(test_obs)}\")\n", "print(f\"Features: 3D accelerometer (X, Y, Z)\")\n", "print(f\"Activities: {len(set(train_activities))} (sitting, walking, running)\")\n", "print()\n", "print(f\"Best Model: {best_n_states} states\")\n", "print(f\"Test Accuracy: {test_results['accuracy']:.3f}\")\n", "print(f\"Test Log-Likelihood: {test_results['log_likelihood']:.2f}\")\n", "print(f\"Model BIC: {results_df.iloc[best_model_idx]['bic']:.2f}\")\n", "print()\n", "print(f\"Forecasting MSE: {hmm_mse:.3f}\")\n", "print(f\"Forecasting Improvement: {((baseline_mse - hmm_mse) / baseline_mse * 100):.1f}% vs baseline\")\n", "print(f\"State Prediction Accuracy: {state_accuracy:.3f}\")\n", "print(\"=\" * 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Limitations and Future Work\n", "\n", "### Limitations:\n", "- **Synthetic Data**: Real accelerometer data may have more noise and complexity\n", "- **Simple Activities**: Only three basic activities considered\n", "- **Gaussian Assumption**: Real sensor data might not be perfectly Gaussian\n", "- **Stationary Assumption**: Activity patterns may change over time\n", "\n", "### Future Improvements:\n", "- Use real accelerometer data from smartphones or wearables\n", "- Include more complex activities (stairs, cycling, etc.)\n", "- Experiment with different emission distributions\n", "- Add semi-supervised learning with partially labeled data\n", "- Implement online learning for adaptive models"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}